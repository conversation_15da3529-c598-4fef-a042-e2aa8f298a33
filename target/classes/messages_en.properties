# General Messages
general.noIssuesFound = No issues found.
general.placeholder.currentLocation = current location
general.placeholder.currentToken = current token

# Lexer Errors
error.lexer.invalidChar = Unrecognized character {0}.
error.lexer.unrecognizedSymbol = Unrecognized symbol or sequence near {0}. ANTLR: {1}

# Parser Errors
error.parser.inputMismatch.expected = Unexpected symbol {0}, expected one of: {1}.
error.parser.inputMismatch.withOffender = Unexpected symbol {0}, expected one of: {1}.
error.parser.inputMismatch.noOffender = Unexpected token, expected one of: {0}.
error.parser.noViableAlt.extracted = The code sequence {0} could not be understood. Please check the syntax in this area.
error.parser.noViableAlt.fallback = The code structure near {0} could not be understood. Please check the syntax.
error.parser.noViableAlt.general = The code structure near {0} could not be understood. Please check the syntax.
error.parser.failedPredicate = A semantic predicate failed near {0}. Predicate: {1}
error.parser.default = Problem near {0}. ANTLR: {1}

# CAPL-specific Parser Errors
error.parser.messageDeclaration.missingModifierOrId = In message declaration for {0}, expected ''*'' or a message ID (e.g., 0x10) after ''message'' keyword, but found identifier directly.
error.parser.messageDeclaration.unexpectedConstantAsQualifier = In message declaration, found unexpected constant {0} where a message qualifier (like an identifier, ''*'', or a specific message ID format) was expected after the ''message'' keyword.
error.parser.messageDeclaration.error = Invalid message declaration syntax near {0}. Expected format: ''message * identifier'' or ''message 0xID identifier''.
error.parser.eventHandler.error = Invalid event handler syntax near {0}. Expected format: ''on eventType eventParameter {{ ... }}''.
error.parser.variableDeclaration.error = Invalid variable declaration syntax near {0}. Check type specifier and variable name.

# CLI Messages
cli.error.noFilesSpecified = No files specified for analysis.
cli.description=Static analysis tool for CAPL scripts.
cli.params.files.description=The CAPL files or directories to analyze.
cli.option.config.description=Path to configuration file.
cli.option.help.description=Show this help message and exit.
cli.option.version.description=Print version information and exit.
cli.option.config.argName=file
cli.usage.syntax=calint [options] <files...>
cli.footer=\nPlease report issues at <your-issue-tracker-url>

# AST Builder Errors
ast.error.paramNameMissing=Parameter declaration is missing a name.
ast.error.param.name.missing=Parameter name is missing.
ast.error.expectedParamDeclNode=Expected a ParameterDeclarationNode, but found {0}.
ast.error.funcNameMissing=Function definition is missing a name.
ast.error.varNameMissing=Variable declaration is missing a name.
ast.error.eventHandlerNotExternalDecl=Expected an external declaration for an event handler, but found something else.
ast.error.funcDefNotExternalDecl=Expected an external declaration for {0}, but found something else.

ast.warn.includesNotExternalDecl=Includes section did not yield an ExternalDeclarationNode
ast.warn.variablesNotExternalDecl=Variables section did not yield an ExternalDeclarationNode
ast.warn.compilationUnitEmpty=Compilation unit contains no declarations or definitions.
ast.warn.blockItemDidNotYieldNode=A block item within a compound statement did not yield an AST node.
ast.warn.unhandledPrimaryExpression=Unhandled primary expression type: {0}
ast.error.funcBodyInvalidType=Body of function {0} was not processed as a BlockNode (actual: {1}).
ast.error.funcBodyMissing=Function definition for {0} is missing its body.

ast.error.emptyCharLiteral=Empty character literal found: {0}.
ast.error.invalidCharLiteral=Invalid character literal: {0}. Expected single character or valid escape sequence.
ast.error.invalidOctalCharLiteral=Invalid octal escape sequence in character literal: {0}.
ast.error.invalidHexCharLiteral=Invalid hexadecimal escape sequence in character literal: {0}.

ast.error.paramNodeInvalidType = Function {0}: Parameter expected to be a ParameterDeclarationNode, but got {1} from context at {2}.
ast.info.kandrParamsFound = Function {0}: K&R style parameter identifiers found at {1}. These parameters are not fully processed as typed parameters.

# Rule: Max Parameters
rule.best_practice.maxParameters.violation = Function {0} has {1} parameters, exceeding the configured maximum of {2}.

# For CalintCli.java (added)
cli.error.parseException=Error parsing command line: {0}
cli.error.fatal.rulesConfigNotFound=FATAL: Rules configuration file {0} not found in {1}. Application cannot start.
cli.option.reportFormat.argName=format
cli.option.reportFormat.description=Set report format (e.g., CONSOLE, JSON, XML).
cli.option.reportOutput.argName=path
cli.option.reportOutput.description=Path to save the report file.
cli.option.ruleDirective.argName=directive
cli.option.ruleDirective.description=Define a rule directive (e.g., ID.severity=LVL, ID.option.key=val). Can be repeated.
cli.option.enableRule.argName=RULE_ID
cli.option.enableRule.description=Enable a specific rule. Can be repeated.
cli.option.disableRule.argName=RULE_ID
cli.option.disableRule.description=Disable a specific rule. Can be repeated.
cli.option.debug.description=Enable debug mode with verbose logging output.
cli.error.noInputFilesFromAnySource=Error: No files specified for analysis from command line or configuration file.
cli.error.noValidInputFilesAfterProcessing=Error: No valid files found for analysis after processing paths.
cli.error.reportGeneration.io=I/O error during report generation: {0}
cli.error.reportGeneration.runtime=Unexpected error during report generation: {0}

# LSP CLI Error Messages
cli.error.lsp.invalidPort=Error: Invalid LSP port number: {0}
cli.error.lsp.portOutOfRange=Error: LSP port must be between 1 and 65535
cli.error.lsp.invalidMode=Error: LSP mode must be 'websocket' or 'stdio'
cli.error.lsp.invalidMaxSessions=Error: Invalid max sessions number: {0}
cli.error.lsp.maxSessionsOutOfRange=Error: Max sessions must be greater than 0
cli.error.lsp.invalidThreadPoolSize=Error: Invalid thread pool size: {0}
cli.error.lsp.threadPoolSizeOutOfRange=Error: Thread pool size must be greater than 0
cli.error.lsp.startupFailed=Error starting Language Server: {0}

# MaxParametersRule specific messages
rule.maxParameters.name=Maximum Function Parameters
rule.maxParameters.desc=Checks for functions with an excessive number of parameters.
issue.maxParameters.exceeded=Function {0} has {1} parameters, which exceeds the configured maximum of {2}.

# Semantic analysis messages
semantic.analysis.start=Starting semantic analysis
semantic.analysis.complete=Semantic analysis completed with {0} issues
semantic.analysis.file.start=Starting semantic analysis for file: {0}
semantic.analysis.file.complete=Completed semantic analysis for file: {0}, found {1} issues
semantic.analysis.null.ast=Skipping semantic analysis for file {0} - AST is null
semantic.analysis.error=Error during semantic analysis of file {0}: {1}
semantic.analysis.fatal.error=Fatal error during semantic analysis: {0}

# Semantic error messages
semantic.error.undeclared.variable=Variable {0} is used but not declared

# Semantic debug messages
semantic.debug.undeclared.variable=Found undeclared variable {0} at {1}
semantic.debug.symbol.defined=Defined symbol {0} of kind {1}
semantic.debug.symbol.redefinition=Symbol {0} is already defined in current scope
semantic.debug.identifier.resolved=Identifier {0} resolved successfully
semantic.debug.enter.file.scope=Entering file scope
semantic.debug.exit.file.scope=Exiting file scope
semantic.debug.enter.function.scope=Entering function scope: {0}
semantic.debug.exit.function.scope=Exiting function scope: {0}
semantic.debug.enter.block.scope=Entering block scope
semantic.debug.exit.block.scope=Exiting block scope
semantic.debug.enter.event.scope=Entering event handler scope
semantic.debug.exit.event.scope=Exiting event handler scope

# Semantic warning messages
semantic.warn.empty.symbol.name=Attempting to define symbol with empty name

# Syntax error messages
syntax.error.invalid.capl.object.declaration=Invalid CAPL object declaration: {0} requires specific syntax. Use: {1}

# Error Analyzer Messages - Invalid Character
analyzer.invalidChar.message.invalidChar=Invalid character: {0}
analyzer.invalidChar.suggestion.nonBreakingSpace=Replace non-breaking space with regular space, tab, or newline
analyzer.invalidChar.suggestion.controlChar=Remove control character. CAPL only supports printable ASCII characters
analyzer.invalidChar.suggestion.highAscii=CAPL only supports ASCII characters (0-127). Replace with ASCII equivalent or remove
analyzer.invalidChar.suggestion.nonEnglishLetter=Character might be from a different alphabet. Use only English letters (A-Z, a-z)
analyzer.invalidChar.suggestion.nonArabicDigit=Use only Arabic numerals (0-9) in CAPL
analyzer.invalidChar.suggestion.symbolConfusion=Use {0} for {1}
analyzer.invalidChar.suggestion.generic=Remove or replace the invalid character with a valid CAPL character
analyzer.invalidChar.suggestion.unexpectedEOF=Unexpected end of file. Check if there are unclosed strings, comments, or blocks
analyzer.invalidChar.suggestion.straightDoubleQuotes=Use straight double quotes (") instead of curly quotes for string literals
analyzer.invalidChar.suggestion.straightSingleQuotes=Use straight single quotes (') instead of curly quotes for character literals
analyzer.invalidChar.suggestion.regularMinusSign=Use regular minus sign (-) instead of en-dash or em-dash
analyzer.invalidChar.suggestion.threePeriods=Use three periods (...) instead of ellipsis character
analyzer.invalidChar.suggestion.regularNewline=Use regular newline (\\n) instead of Unicode line/paragraph separator
analyzer.invalidChar.suggestion.replaceWithAscii=Replace with standard ASCII symbol

# Error Analyzer Messages - Token Error
analyzer.token.message.incompleteHex=Incomplete hexadecimal number
analyzer.token.suggestion.incompleteHex=Hexadecimal numbers must have at least one digit after 0x
analyzer.token.message.invalidHex=Invalid hexadecimal number format
analyzer.token.suggestion.invalidHex=Hexadecimal numbers should contain only digits 0-9 and letters A-F
analyzer.token.message.invalidFloat=Invalid floating-point number format
analyzer.token.suggestion.multipleDecimal=Floating-point numbers cannot have multiple decimal points
analyzer.token.suggestion.invalidFloat=Check decimal point placement and digit format
analyzer.token.message.invalidInteger=Invalid integer format
analyzer.token.suggestion.invalidInteger=Integer should contain only digits
analyzer.token.message.invalidNumeric=Invalid numeric literal
analyzer.token.suggestion.invalidNumeric=Check the number format
analyzer.token.message.incompleteSystemVar=Incomplete system variable reference
analyzer.token.suggestion.incompleteSystemVar=Add variable name after @ symbol
analyzer.token.message.invalidSystemVar=Invalid system variable reference
analyzer.token.suggestion.invalidSystemVar=System variables should follow the format @VariableName
analyzer.token.message.incompleteSignal=Incomplete signal reference
analyzer.token.suggestion.incompleteSignal=Add signal name after $ symbol
analyzer.token.message.invalidSignal=Invalid signal reference
analyzer.token.suggestion.invalidSignal=Signal references should follow the format $SignalName

# Error Analyzer Messages - Token Error (Additional)
analyzer.token.suggestion.removeInvalidChar=Remove the invalid character
analyzer.token.message.invalidCharacter=Invalid character: {0}
analyzer.token.message.unrecognizedToken=Unrecognized token: {0}
analyzer.token.message.stringLiteralError=String literal error
analyzer.token.suggestion.checkStringLiteralSyntax=Check string literal syntax
analyzer.token.message.unclosedStringLiteral=Unclosed string literal
analyzer.token.suggestion.addClosingDoubleQuote=Add closing quote (") at the end of the string
analyzer.token.message.unclosedCharLiteral=Unclosed character literal
analyzer.token.suggestion.addClosingSingleQuote=Add closing quote (') at the end of the character
analyzer.token.message.invalidEscapeSequence=Invalid escape sequence in string literal
analyzer.token.suggestion.checkEscapeSequences=Check escape sequences (\\n, \\t, \\r, \\\\, \\", \\')
analyzer.token.message.numericLiteralError=Numeric literal error
analyzer.token.suggestion.checkNumericLiteralFormat=Check numeric literal format
analyzer.token.message.caplTokenError=CAPL token error
analyzer.token.suggestion.checkCaplSpecificSyntax=Check CAPL-specific syntax
analyzer.token.message.lexerError=Lexer error ({0})
analyzer.token.message.lexerErrorAtToken=Lexer error ({0}) at token: {1}
analyzer.token.suggestion.checkSyntaxLocation=Check the syntax around this location
analyzer.token.suggestion.useDoubleQuotes=Use double quotes for string literals: "text"
analyzer.token.suggestion.useStraightQuotes=Use straight quotes instead of curly quotes: ' or "
analyzer.token.suggestion.useRegularMinus=Use regular minus sign (-) instead of dash
analyzer.token.suggestion.replaceUnusualWhitespace=Replace unusual whitespace with regular space or tab
analyzer.token.suggestion.onlyAsciiCharacters=CAPL only supports ASCII characters
analyzer.token.suggestion.removeOrReplaceInvalidChar=Remove or replace the invalid character
analyzer.token.suggestion.checkSyntaxAtLocation=Check the syntax at this location
analyzer.token.suggestion.misspelledMessage=Did you mean 'message'?
analyzer.token.suggestion.misspelledSignal=Did you mean 'signal'?
analyzer.token.suggestion.useMsTimer=Use 'msTimer' for millisecond timers
analyzer.token.suggestion.useIncludeDirective=Use '#include' for including files
analyzer.token.suggestion.checkIdentifierSpelling=Check if this identifier is spelled correctly
analyzer.token.suggestion.checkTokenSyntax=Check the token syntax

# Error Analyzer Messages - CAPL Token
analyzer.caplToken.message.incompleteSystemVar=Incomplete system variable reference
analyzer.caplToken.suggestion.incompleteSystemVar=Add variable name after @ symbol (e.g., @SysVar)
analyzer.caplToken.message.invalidSystemVarName=Invalid system variable name
analyzer.caplToken.suggestion.invalidSystemVarName=System variable names must be valid identifiers (letters, digits, underscores)
analyzer.caplToken.message.systemVarFormat=System variable reference format
analyzer.caplToken.suggestion.systemVarNamespace=Use format @Namespace::VariableName for namespaced system variables
analyzer.caplToken.suggestion.systemVarContext=Check if this system variable is used in the correct context
analyzer.caplToken.message.incompleteSignal=Incomplete signal reference
analyzer.caplToken.suggestion.incompleteSignal=Add signal name after $ symbol (e.g., $MySignal)
analyzer.caplToken.message.invalidSignalName=Invalid signal name
analyzer.caplToken.suggestion.invalidSignalName=Signal names must be valid identifiers (letters, digits, underscores)
analyzer.caplToken.message.signalFormat=Signal reference format
analyzer.caplToken.suggestion.signalDatabase=Use format $Database::SignalName for database-qualified signals
analyzer.caplToken.suggestion.signalContext=Check if this signal reference is used in the correct context

# NoViableAltAnalyzer messages
analyzer.noViableAlt.message.invalidNumberFormat=Invalid expression: {0} is not a valid expression (appears to be a malformed number)
analyzer.noViableAlt.suggestion.invalidNumberFormat=Use a valid number literal with only one decimal point (e.g., 12.1 or 12.2) or separate the expression properly
analyzer.noViableAlt.message.incompleteStatement=Incomplete statement: {0} appears to be missing syntax elements
analyzer.noViableAlt.suggestion.incompleteStatement=Check for missing semicolons, operators, or other required syntax elements
analyzer.noViableAlt.message.generic=The code sequence {0} could not be understood
analyzer.noViableAlt.suggestion.generic=Please check the syntax in this area and ensure all required elements are present
analyzer.caplToken.message.incompletePreprocessor=Incomplete preprocessor directive
analyzer.caplToken.suggestion.incompletePreprocessor=Add directive name after # symbol (e.g., #include, #define)
analyzer.caplToken.message.misspelledInclude=Misspelled preprocessor directive
analyzer.caplToken.suggestion.misspelledInclude=Did you mean #include?
analyzer.caplToken.message.misspelledDefine=Misspelled preprocessor directive
analyzer.caplToken.suggestion.misspelledDefine=Did you mean #define?
analyzer.caplToken.message.misspelledPragma=Misspelled preprocessor directive
analyzer.caplToken.suggestion.misspelledPragma=Did you mean #pragma?
analyzer.caplToken.message.unknownPreprocessor=Unknown preprocessor directive: {0}
analyzer.caplToken.suggestion.unknownPreprocessor=Valid directives: #include, #define, #pragma, #ifdef, #ifndef, #endif
analyzer.caplToken.message.incompleteEvent=Incomplete event keyword
analyzer.caplToken.suggestion.incompleteEvent=Did you mean 'on'?
analyzer.caplToken.message.misspelledSysvar=Misspelled event keyword
analyzer.caplToken.suggestion.misspelledSysvar=Did you mean 'sysvar'?
analyzer.caplToken.message.misspelledEnvvar=Misspelled event keyword
analyzer.caplToken.suggestion.misspelledEnvvar=Did you mean 'envvar'?
analyzer.caplToken.message.unknownEvent=Unknown event keyword: {0}
analyzer.caplToken.suggestion.unknownEvent=Valid event keywords: on, sysvar, envvar, timer, key, etc.
analyzer.caplToken.message.misspelledFlexray=Misspelled FlexRay keyword
analyzer.caplToken.suggestion.misspelledFlexray=Did you mean 'flexray'?
analyzer.caplToken.message.misspelledFrFrame=Misspelled FlexRay frame keyword
analyzer.caplToken.suggestion.misspelledFrFrame=Did you mean 'frFrame'?
analyzer.caplToken.message.misspelledLinFrame=Misspelled LIN frame keyword
analyzer.caplToken.suggestion.misspelledLinFrame=Did you mean 'linFrame'?
analyzer.caplToken.message.misspelledDiagRequest=Misspelled diagnostic keyword
analyzer.caplToken.suggestion.misspelledDiagRequest=Did you mean 'diagRequest'?
analyzer.caplToken.message.unknownBusKeyword=Unknown bus-specific keyword: {0}
analyzer.caplToken.suggestion.unknownBusKeyword=Check documentation for valid bus-specific keywords
analyzer.caplToken.message.genericCaplToken=Invalid CAPL token: {0}
analyzer.caplToken.suggestion.genericCaplToken=Check CAPL-specific syntax
analyzer.caplToken.message.unknownCaplToken=Unknown CAPL token error
analyzer.caplToken.suggestion.unknownCaplToken=Check CAPL-specific syntax

# Error Analyzer Messages - Numeric Literal
analyzer.numeric.message.incompleteHex=Incomplete hexadecimal number
analyzer.numeric.suggestion.incompleteHex=Hexadecimal numbers must have at least one digit after 0x
analyzer.numeric.message.invalidHex=Invalid hexadecimal number format
analyzer.numeric.suggestion.invalidHex=Hexadecimal numbers should start with 0x or 0X followed by digits 0-9 and letters A-F
analyzer.numeric.message.invalidHexChar=Invalid character {0} in hexadecimal number
analyzer.numeric.suggestion.invalidHexChar=Hexadecimal numbers can only contain digits 0-9 and letters A-F (case insensitive)
analyzer.numeric.message.floatStartsWithDot=Floating-point number cannot start with decimal point
analyzer.numeric.suggestion.floatStartsWithDot=Add at least one digit before the decimal point (e.g., 0.5)
analyzer.numeric.message.floatEndsWithDot=Floating-point number cannot end with decimal point
analyzer.numeric.suggestion.floatEndsWithDot=Add at least one digit after the decimal point (e.g., 5.0)
analyzer.numeric.message.multipleDecimal=Floating-point number has multiple decimal points
analyzer.numeric.suggestion.multipleDecimal=Use only one decimal point in floating-point numbers
analyzer.numeric.message.invalidFloatChar=Invalid character {0} in floating-point number
analyzer.numeric.suggestion.invalidFloatChar=Floating-point numbers can only contain digits 0-9 and one decimal point
analyzer.numeric.message.scientificNotation=Scientific notation is not supported
analyzer.numeric.suggestion.scientificNotation=Use standard decimal notation instead of scientific notation
analyzer.numeric.message.invalidOctal=Invalid octal number format
analyzer.numeric.suggestion.invalidOctal=CAPL does not support octal numbers. Use decimal or hexadecimal (0x) format
analyzer.numeric.message.invalidIntegerChar=Invalid character {0} in integer
analyzer.numeric.suggestion.invalidIntegerChar=Integers can only contain digits 0-9
analyzer.numeric.message.numberTooBig=Integer value is too large
analyzer.numeric.suggestion.numberTooBig=Use a smaller number or consider using a different data type
analyzer.numeric.message.genericNumeric=Unknown numeric literal error
analyzer.numeric.suggestion.genericNumeric=Check numeric literal format

# Error Analyzer Messages - String Literal
analyzer.string.message.unclosedString=Unclosed string literal
analyzer.string.suggestion.unclosedString=Add closing double quote (") at the end of the string
analyzer.string.suggestion.multilineString=String literals cannot span multiple lines. Use \\n for newlines or close the string
analyzer.string.suggestion.backslashEnd=String ends with backslash. Add closing quote or escape the backslash (\\\\)
analyzer.string.message.unclosedChar=Unclosed character literal
analyzer.string.suggestion.unclosedChar=Add closing single quote (') at the end of the character
analyzer.string.suggestion.multiChar=Character literals can only contain one character. Use double quotes for strings
analyzer.string.message.invalidEscape=Invalid escape sequence: {0}
analyzer.string.suggestion.escapeTab=Use \\t for tab or regular space character
analyzer.string.suggestion.escapeDigit=Use \\n for newline or regular digit character
analyzer.string.suggestion.escapeWord=Use regular word characters or \\n for newline
analyzer.string.suggestion.escapeChar=Use \\n for newline or regular 'c' character
analyzer.string.suggestion.escapeHex=Hexadecimal escapes are not supported in CAPL
analyzer.string.suggestion.escapeUnicode=Unicode escapes are not supported in CAPL
analyzer.string.suggestion.escapeValid=Valid escape sequences: \\n, \\t, \\r, \\\\, \\", \\', \\0, \\b, \\f, \\v, \\a
analyzer.string.message.emptyChar=Empty character literal
analyzer.string.suggestion.emptyChar=Character literals must contain exactly one character, e.g., 'a' or '\\n'
analyzer.string.message.multiChar=Character literal contains multiple characters
analyzer.string.suggestion.multiChar=Character literals can only contain one character. Use double quotes for strings
analyzer.string.message.invalidChars=String contains invalid characters
analyzer.string.suggestion.invalidChars=Remove or escape invalid characters in the string
analyzer.string.suggestion.nullChar=Null characters (\\0) are not allowed in string literals
analyzer.string.suggestion.controlChars=Control characters should be escaped (e.g., \\n, \\t, \\r)
analyzer.string.message.genericString=Unknown string literal error
analyzer.string.suggestion.genericString=Check string literal syntax

# Error Analyzer Messages - Unrecognized Token
analyzer.unrecognized.message.identifierStartsWithDigit=Invalid identifier: cannot start with digit
analyzer.unrecognized.suggestion.identifierStartsWithDigit=Identifiers must start with a letter or underscore
analyzer.unrecognized.message.misspelledKeyword=Possible misspelled keyword: {0}
analyzer.unrecognized.suggestion.misspelledKeyword=Did you mean {0}?
analyzer.unrecognized.message.unrecognizedToken=Unrecognized token: {0}
analyzer.unrecognized.suggestion.unrecognizedToken=Check spelling and syntax
analyzer.unrecognized.message.genericUnrecognized=Lexer error
analyzer.unrecognized.suggestion.genericUnrecognized=Check token syntax and spelling
analyzer.unrecognized.suggestion.misspelledInt=Did you mean ''int''?
analyzer.unrecognized.suggestion.misspelledFloat=Did you mean ''float''?
analyzer.unrecognized.suggestion.misspelledDouble=Did you mean ''double''?
analyzer.unrecognized.suggestion.misspelledChar=Did you mean ''char''?
analyzer.unrecognized.suggestion.misspelledVoid=Did you mean ''void''?
analyzer.unrecognized.suggestion.misspelledIf=Did you mean ''if''?
analyzer.unrecognized.suggestion.misspelledElse=Did you mean ''else''?
analyzer.unrecognized.suggestion.misspelledWhile=Did you mean ''while''?
analyzer.unrecognized.suggestion.misspelledFor=Did you mean ''for''?
analyzer.unrecognized.suggestion.misspelledReturn=Did you mean ''return''?
analyzer.unrecognized.suggestion.misspelledBreak=Did you mean ''break''?
analyzer.unrecognized.suggestion.misspelledContinue=Did you mean ''continue''?
analyzer.unrecognized.suggestion.misspelledSwitch=Did you mean ''switch''?
analyzer.unrecognized.suggestion.misspelledCase=Did you mean ''case''?
analyzer.unrecognized.suggestion.misspelledDefault=Did you mean ''default''?
analyzer.unrecognized.suggestion.misspelledOn=Did you mean ''on''?
analyzer.unrecognized.suggestion.misspelledMessage=Did you mean ''message''?
analyzer.unrecognized.suggestion.misspelledTimer=Did you mean ''timer''?
analyzer.unrecognized.suggestion.misspelledKey=Did you mean ''key''?
analyzer.unrecognized.suggestion.misspelledSysvar=Did you mean ''sysvar''?
analyzer.unrecognized.suggestion.misspelledEnvvar=Did you mean ''envvar''?
analyzer.unrecognized.suggestion.misspelledVariables=Did you mean ''variables''?
analyzer.unrecognized.suggestion.misspelledIncludes=Did you mean ''includes''?
analyzer.unrecognized.suggestion.misspelledFunctions=Did you mean ''functions''?
analyzer.unrecognized.suggestion.misspelledTestcase=Did you mean ''testcase''?
analyzer.unrecognized.suggestion.misspelledTestset=Did you mean ''testset''?
analyzer.unrecognized.suggestion.misspelledTestgroup=Did you mean ''testgroup''?

# Error Analyzer Messages - Unrecognized Token (Additional)
analyzer.unrecognized.suggestion.identifierHyphens=Identifiers cannot contain hyphens. Use underscores (_) instead
analyzer.unrecognized.suggestion.identifierSpaces=Identifiers cannot contain spaces
analyzer.unrecognized.message.invalidNumberFormat=Invalid number format: {0}
analyzer.unrecognized.suggestion.checkNumberFormat=Check the number format
analyzer.unrecognized.suggestion.hexNumberFormat=Hexadecimal numbers should contain only digits 0-9 and letters A-F
analyzer.unrecognized.suggestion.decimalPointPlacement=Check decimal point placement and ensure all characters are digits
analyzer.unrecognized.suggestion.numbersOnlyDigits=Numbers should contain only digits 0-9
analyzer.unrecognized.message.invalidStringLiteral=Invalid string literal: {0}
analyzer.unrecognized.suggestion.checkStringLiteralSyntax=Check string literal syntax
analyzer.unrecognized.suggestion.stringMissingClosingQuote=String literal is missing closing quote (")
analyzer.unrecognized.suggestion.charMissingClosingQuote=Character literal is missing closing quote (')
analyzer.unrecognized.suggestion.checkEscapeSequences=Check escape sequences in string literal

# Error Analyzer Messages - Lexer Error (Base Class)
analyzer.lexer.message.genericError=Lexer error
analyzer.lexer.suggestion.genericError=Check syntax and spelling
analyzer.lexer.message.noViableAlt=No viable alternative at input {0}
analyzer.lexer.suggestion.noViableAlt=Check character validity and syntax
analyzer.lexer.message.inputMismatch=Input mismatch at {0}
analyzer.lexer.suggestion.inputMismatch=Check token syntax and spelling
analyzer.lexer.message.failedPredicate=Failed predicate at {0}
analyzer.lexer.suggestion.failedPredicate=Check context-sensitive syntax rules
analyzer.lexer.message.recognitionError=Recognition error at {0}
analyzer.lexer.suggestion.recognitionError=Check syntax and token validity

# Fallback analyzer messages
analyzer.fallback.message.analysisFailed=Analysis failed with {0}: {1}
analyzer.fallback.suggestion.checkSyntax=Please check the syntax and try again

# Structural Error Analyzer Messages
analyzer.structural.message.missingProgramStructure=Missing program structure elements
analyzer.structural.suggestion.missingProgramStructure=Add required sections like 'variables' or 'includes' to define the program structure
analyzer.structural.message.incorrectSectionOrder=Incorrect section order: includes section must come before variables section
analyzer.structural.suggestion.incorrectSectionOrder=Move the includes section before the variables section
analyzer.structural.message.missingSectionBraces=Missing opening brace after section declaration
analyzer.structural.suggestion.missingSectionBraces=Add opening brace '{' after section name (e.g., variables {)
analyzer.structural.message.malformedInclude=Malformed include statement: {0}
analyzer.structural.suggestion.malformedInclude=Use correct include syntax: #include "filename.h" or #include <filename.h>
analyzer.structural.message.missingIncludeQuotes=Missing quotes in include statement: {0}
analyzer.structural.suggestion.missingIncludeQuotes=Enclose filename in quotes: #include "filename.h"
analyzer.structural.message.missingVariableDeclaration=Missing or incomplete variable declaration
analyzer.structural.suggestion.missingVariableDeclaration=Complete the variable declaration with type, name, and semicolon (e.g., int variable;)
analyzer.structural.message.unmatchedBraces=Unmatched braces in code block
analyzer.structural.suggestion.unmatchedBraces=Check that every opening brace '{' has a corresponding closing brace '}'
analyzer.structural.message.missingSemicolon=Missing semicolon at end of statement
analyzer.structural.suggestion.missingSemicolon=Add semicolon ';' at the end of the statement
analyzer.structural.message.generic=Structural error in {0}: {1}
analyzer.structural.suggestion.generic=Check the structure and syntax of the {0}

# Declaration Error Analyzer Messages
analyzer.declaration.message.missingTypeSpecifier=Missing type specifier in variable declaration
analyzer.declaration.suggestion.missingTypeSpecifier=Add a type specifier (e.g., int, float, char) before the variable name
analyzer.declaration.message.invalidVariableName=Invalid variable name: {0}
analyzer.declaration.suggestion.invalidVariableName=Use a valid identifier starting with a letter or underscore, followed by letters, digits, or underscores
analyzer.declaration.message.missingSemicolon=Missing semicolon at end of declaration
analyzer.declaration.suggestion.missingSemicolon=Add semicolon '';'' at the end of the declaration
analyzer.declaration.message.invalidBuiltinType=Invalid built-in type: {0}
analyzer.declaration.suggestion.invalidBuiltinType=Use a valid built-in type: {1}
analyzer.declaration.message.invalidTypeModifier=Invalid type modifier: {0}
analyzer.declaration.suggestion.invalidTypeModifier=Use a valid type modifier: {1} (Note: CAPL uses ''stack'' instead of ''static'')
analyzer.declaration.message.missingFunctionName=Missing function name in function declaration
analyzer.declaration.suggestion.missingFunctionName=Add a function name after the return type
analyzer.declaration.message.missingParentheses=Missing parentheses in function declaration
analyzer.declaration.suggestion.missingParentheses=Add opening parenthesis ''('' after the function name
analyzer.declaration.message.invalidParameterSyntax=Invalid parameter syntax in function declaration
analyzer.declaration.suggestion.invalidParameterSyntax=Check parameter list syntax: type name, type name, ...
analyzer.declaration.message.incompleteDeclaration=Incomplete declaration
analyzer.declaration.suggestion.incompleteDeclaration=Complete the declaration with proper syntax
analyzer.declaration.message.invalidCaplObjectType=Invalid CAPL object type: {0}
analyzer.declaration.suggestion.invalidCaplObjectType=Use a valid CAPL object type: {1}
analyzer.declaration.message.generic=Declaration error in {0}: {1}
analyzer.declaration.suggestion.generic=Check the syntax and structure of the {0}

# Const Literal Declaration Error Analyzer Messages
analyzer.constLiteral.message.missingConstKeyword=Missing ''const'' keyword in constant declaration
analyzer.constLiteral.suggestion.missingConstKeyword=Add ''const'' keyword at the beginning of the declaration
analyzer.constLiteral.message.missingVariableName=Missing variable name in constant declaration
analyzer.constLiteral.suggestion.missingVariableName=Add a variable name after ''const'' keyword
analyzer.constLiteral.message.missingAssignmentOperator=Missing assignment operator in constant declaration
analyzer.constLiteral.suggestion.missingAssignmentOperator=Add ''='' after the variable name
analyzer.constLiteral.message.missingLiteralValue=Missing literal value in constant declaration
analyzer.constLiteral.suggestion.missingLiteralValue=Add a literal value after the assignment operator
analyzer.constLiteral.message.invalidLiteralValue=Invalid literal value: {0}
analyzer.constLiteral.suggestion.invalidLiteralValue=Use a valid literal value (number, string, or character)
analyzer.constLiteral.message.missingSemicolon=Missing semicolon at end of constant declaration
analyzer.constLiteral.suggestion.missingSemicolon=Add semicolon '';'' at the end of the declaration
analyzer.constLiteral.message.generic=Constant literal declaration error: {0}
analyzer.constLiteral.suggestion.generic=Check the syntax of the constant declaration: const name = value;

# Associative Array Declaration Error Analyzer Messages
analyzer.associativeArray.message.missingValueType=Missing value type in associative array declaration
analyzer.associativeArray.suggestion.missingValueType=Add a value type (e.g., int, char, float) at the beginning
analyzer.associativeArray.message.invalidValueType=Invalid value type: {0}
analyzer.associativeArray.suggestion.invalidValueType=Use a valid value type: {1}
analyzer.associativeArray.message.missingArrayName=Missing array name in associative array declaration
analyzer.associativeArray.suggestion.missingArrayName=Add an array name after the value type
analyzer.associativeArray.message.missingOpeningBracket=Missing opening bracket in associative array declaration
analyzer.associativeArray.suggestion.missingOpeningBracket=Add opening bracket ''['' after the array name
analyzer.associativeArray.message.missingKeyType=Missing key type in associative array declaration
analyzer.associativeArray.suggestion.missingKeyType=Add a key type inside the brackets (e.g., char, int)
analyzer.associativeArray.message.invalidKeyType=Invalid key type: {0}
analyzer.associativeArray.suggestion.invalidKeyType=Use a valid key type: {1}
analyzer.associativeArray.message.missingKeyArrayBrackets=Missing array brackets for key type
analyzer.associativeArray.suggestion.missingKeyArrayBrackets=Add ''[]'' after the key type
analyzer.associativeArray.message.missingClosingBracket=Missing closing bracket in associative array declaration
analyzer.associativeArray.suggestion.missingClosingBracket=Add closing bracket '']'' to complete the declaration
analyzer.associativeArray.message.missingSemicolon=Missing semicolon at end of associative array declaration
analyzer.associativeArray.suggestion.missingSemicolon=Add semicolon '';'' at the end of the declaration
analyzer.associativeArray.message.generic=Associative array declaration error: {0}
analyzer.associativeArray.suggestion.generic=Check the syntax: type arrayName[keyType[]]

# Type Definition Error Analyzer Messages
analyzer.typeDefinition.message.missingTypeKeyword=Missing type definition keyword
analyzer.typeDefinition.suggestion.missingTypeKeyword=Add a type keyword (struct, enum, or union)
analyzer.typeDefinition.message.missingTypeName=Missing type name in {0} definition
analyzer.typeDefinition.suggestion.missingTypeName=Add a type name after the {0} keyword
analyzer.typeDefinition.message.invalidTypeName=Invalid type name: {0}
analyzer.typeDefinition.suggestion.invalidTypeName=Use a valid identifier for the type name
analyzer.typeDefinition.message.missingOpeningBrace=Missing opening brace in {0} definition
analyzer.typeDefinition.suggestion.missingOpeningBrace=Add opening brace ''{{'' after the {0} name
analyzer.typeDefinition.message.missingClosingBrace=Missing closing brace in {0} definition
analyzer.typeDefinition.suggestion.missingClosingBrace=Add closing brace ''}}'' to complete the {0} definition
analyzer.typeDefinition.message.missingSemicolon=Missing semicolon at end of type definition
analyzer.typeDefinition.suggestion.missingSemicolon=Add semicolon '';'' after the closing brace
analyzer.typeDefinition.message.invalidMemberDeclaration=Invalid member declaration in {0}
analyzer.typeDefinition.suggestion.invalidMemberDeclaration=Use proper member syntax: type memberName; inside the {0}
analyzer.typeDefinition.message.invalidEnumValue=Invalid enum value: {0}
analyzer.typeDefinition.suggestion.invalidEnumValue=Use valid identifiers for enum values
analyzer.typeDefinition.message.generic={0} definition error: {1}
analyzer.typeDefinition.suggestion.generic=Check the syntax of the {0} definition

# CAPL Object Type Error Analyzer Messages
analyzer.caplObject.generic.message=CAPL {0} object type error: {1}
analyzer.caplObject.generic.suggestion=Check the syntax of the {0} object type declaration
analyzer.caplObject.generic.invalidWildcard=Invalid wildcard usage: {0}
analyzer.caplObject.generic.invalidWildcardSuggestion=Wildcard ''*'' can only be used with object type keywords
analyzer.caplObject.generic.invalidObjectType=Invalid CAPL object type: {0}
analyzer.caplObject.generic.invalidObjectTypeSuggestion=Use a valid CAPL object type: {1}

# Message Type Error Messages
analyzer.caplObject.message.missingIdentifier=Missing message identifier
analyzer.caplObject.message.missingIdentifierSuggestion=Add a message identifier: ''*'', message ID (0x100), or message name
analyzer.caplObject.message.invalidId=Invalid message ID format: {0}
analyzer.caplObject.message.invalidIdSuggestion=Use valid format: ''*'', hex ID (0x100), decimal ID (123), or qualified name (DB.MessageName)
analyzer.caplObject.message.invalidQualifiedId=Invalid qualified message identifier: {0}
analyzer.caplObject.message.invalidQualifiedIdSuggestion=Use format: DatabaseName.MessageName

# Signal Type Error Messages
analyzer.caplObject.signal.missingIdentifier=Missing signal identifier
analyzer.caplObject.signal.missingIdentifierSuggestion=Add a signal identifier: ''*'' or signal name
analyzer.caplObject.signal.invalidName=Invalid signal name: {0}
analyzer.caplObject.signal.invalidNameSuggestion=Use a valid signal name or ''*'' for wildcard

# FlexRay Type Error Messages
analyzer.caplObject.flexray.missingParameters=Missing FlexRay parameters
analyzer.caplObject.flexray.missingParametersSuggestion=Add FlexRay parameters in format: (channel,slot,cycle)
analyzer.caplObject.flexray.invalidParameterFormat=Invalid FlexRay parameter format: {0}
analyzer.caplObject.flexray.invalidParameterFormatSuggestion=Use format: (channel,slot,cycle) with numeric values

# Diagnostic Type Error Messages
analyzer.caplObject.diagnostic.missingParameter=Missing diagnostic parameter
analyzer.caplObject.diagnostic.missingParameterSuggestion=Add diagnostic parameter: ''this'', string literal, or system variable

# PDU Type Error Messages
analyzer.caplObject.pdu.missingSizeSpecifier=Missing PDU size specifier
analyzer.caplObject.pdu.missingSizeSpecifierSuggestion=Add PDU size: ''short'' or ''long'' followed by ID
analyzer.caplObject.pdu.invalidSize=Invalid PDU size: {0}
analyzer.caplObject.pdu.invalidSizeSuggestion=Use ''short'' or ''long'' followed by hex (0x10) or decimal ID

# System Variable Type Error Messages
analyzer.caplObject.sysvar.missingIdentifier=Missing system variable identifier
analyzer.caplObject.sysvar.missingIdentifierSuggestion=Add system variable identifier after the type

# Message Type Error Analyzer Messages (T012A)
analyzer.messageType.message.missingKeyword=Missing message type keyword
analyzer.messageType.message.missingKeywordSuggestion=Add a message type keyword: ''message'', ''multiplexedMessage'', or ''linFrame''
analyzer.messageType.message.missingIdentifier=Missing message identifier for {0}
analyzer.messageType.message.missingIdentifierSuggestion=Add a message identifier: ''*'', hex ID (0x100), decimal ID (123), message name, or database qualified name (DB.MessageName)
analyzer.messageType.message.invalidWildcard=Invalid wildcard usage: {0}
analyzer.messageType.message.invalidWildcardSuggestion=Wildcard ''*'' can only be used after message type keywords
analyzer.messageType.message.invalidHexId=Invalid hexadecimal ID format: {0}
analyzer.messageType.message.invalidHexIdSuggestion=Use valid hex format: 0x followed by hexadecimal digits (e.g., 0x100, 0xABC)
analyzer.messageType.message.invalidDecimalId=Invalid decimal ID format: {0}
analyzer.messageType.message.invalidDecimalIdSuggestion=Use valid decimal format: digits only (e.g., 123, 456)
analyzer.messageType.message.invalidQualifiedId=Invalid qualified identifier: {0}
analyzer.messageType.message.invalidQualifiedIdSuggestion=Use format: DatabaseName.MessageName or MessageName.Property
analyzer.messageType.message.invalidDatabaseQualified=Invalid database qualified format: {0}
analyzer.messageType.message.invalidDatabaseQualifiedSuggestion=Use format: DatabaseName.0x100 or DatabaseName.123
analyzer.messageType.message.invalidName=Invalid message name: {0}
analyzer.messageType.message.invalidNameSuggestion=Use a valid identifier, hex ID (0x100), decimal ID (123), wildcard (*), or qualified name
analyzer.messageType.message.generic={0} type error: {1}
analyzer.messageType.message.genericSuggestion=Check the syntax of the {0} declaration

# Signal Type Error Analyzer Messages (T012B)
analyzer.signalType.signal.missingKeyword=Missing signal type keyword
analyzer.signalType.signal.missingKeywordSuggestion=Add the ''signal'' keyword before the signal identifier
analyzer.signalType.signal.missingIdentifier=Missing signal identifier
analyzer.signalType.signal.missingIdentifierSuggestion=Add a signal identifier: ''*'' for wildcard or signal name (e.g., EngineSpeed, MyMessage::RPM)
analyzer.signalType.signal.invalidWildcard=Invalid wildcard usage: {0}
analyzer.signalType.signal.invalidWildcardSuggestion=Wildcard ''*'' can only be used after the ''signal'' keyword
analyzer.signalType.signal.invalidName=Invalid signal name: {0}
analyzer.signalType.signal.invalidNameSuggestion=Use a valid signal identifier, wildcard (*), or qualified name (Message::SignalName)
analyzer.signalType.signal.invalidNamespace=Invalid namespace qualifier: {0}
analyzer.signalType.signal.invalidNamespaceSuggestion=Use correct namespace format: Namespace::SignalName or Node::Message::SignalName
analyzer.signalType.signal.incompleteQualified=Incomplete qualified signal name: {0}
analyzer.signalType.signal.incompleteQualifiedSuggestion=Complete the qualified name: Namespace::SignalName
analyzer.signalType.signal.generic=Signal type error: {0}
analyzer.signalType.signal.genericSuggestion=Check the syntax of the signal declaration

# Other CAPL Object Type Error Analyzer Messages (T012C)
# FlexRay Type Errors
analyzer.otherCaplObject.flexray.missingIdentifier=Missing FlexRay identifier for {0}
analyzer.otherCaplObject.flexray.missingIdentifierSuggestion=Add a FlexRay identifier: ''*'', constant (0x100), qualified name, or channel/slot/cycle format (1,0,1)
analyzer.otherCaplObject.flexray.invalidFormat=Invalid FlexRay format: {0}
analyzer.otherCaplObject.flexray.invalidFormatSuggestion=Use valid FlexRay format: (channel,slot,cycle) with numeric values

# Diagnostic Type Errors
analyzer.otherCaplObject.diagnostic.missingIdentifier=Missing diagnostic identifier for {0}
analyzer.otherCaplObject.diagnostic.missingIdentifierSuggestion=Add a diagnostic identifier: qualified name, system variable, ''this'', ''*'', or string literal

# PDU Type Errors
analyzer.otherCaplObject.pdu.missingIdentifier=Missing PDU identifier
analyzer.otherCaplObject.pdu.missingIdentifierSuggestion=Add a PDU identifier: ''short 0x10'', ''long 0x12345'', constant, identifier, or ''*''

# System Variable Type Errors
analyzer.otherCaplObject.sysvar.missingStar=Missing ''*'' for {0}
analyzer.otherCaplObject.sysvar.missingStarSuggestion=System variable types must be followed by ''*'' (e.g., {0}*)

# Database Type Errors
analyzer.otherCaplObject.database.missingStar=Missing ''*'' for {0}
analyzer.otherCaplObject.database.missingStarSuggestion=Database types must be followed by ''*'' (e.g., {0}*)

# SOME/IP Type Errors
analyzer.otherCaplObject.someip.missingStar=Missing ''*'' for {0}
analyzer.otherCaplObject.someip.missingStarSuggestion=SOME/IP types must be followed by ''*'' (e.g., {0}*)

# Other Type Errors
analyzer.otherCaplObject.other.missingIdentifier=Missing identifier for {0}
analyzer.otherCaplObject.other.missingIdentifierSuggestion=Add an appropriate identifier for the {0} type

# Generic Other CAPL Object Type Errors
analyzer.otherCaplObject.generic={0} type error: {1}
analyzer.otherCaplObject.genericSuggestion=Check the syntax of the {0} declaration

# Event Handler Error Analyzer Messages (T013)
analyzer.eventHandler.missingOnKeyword=Missing ''on'' keyword for event handler
analyzer.eventHandler.missingOnKeywordSuggestion=Add ''on'' keyword before the event type (e.g., ''on message'', ''on timer'')
analyzer.eventHandler.missingEventType=Missing event type after ''on'' keyword
analyzer.eventHandler.missingEventTypeSuggestion=Add an event type: ''message'', ''timer'', ''signal'', ''sysvar'', ''frFrame'', etc.
analyzer.eventHandler.missingCompoundStatement=Missing compound statement for event handler
analyzer.eventHandler.missingCompoundStatementSuggestion=Add a compound statement with curly braces: { /* handler code */ }

# System Variable Event Errors
analyzer.eventHandler.sysvar.missingParameter=Missing parameter for {0} event
analyzer.eventHandler.sysvar.missingParameterSuggestion=Add a system variable parameter: namespace::variable or variable name

# FlexRay Event Errors
analyzer.eventHandler.flexray.missingParameter=Missing parameter for {0} event
analyzer.eventHandler.flexray.missingParameterSuggestion=Add a FlexRay parameter: frame name, PDU name, or wildcard (*)

# Qualified Event Type Errors
analyzer.eventHandler.qualified.missingParameter=Missing parameter for {0} event
analyzer.eventHandler.qualified.missingParameterSuggestion=Add an appropriate parameter for the {0} event type

# Generic Event Handler Errors
analyzer.eventHandler.generic={0} event handler error: {1}
analyzer.eventHandler.genericSuggestion=Check the syntax of the {0} event handler declaration

# Statement Error Analyzer Messages (T013A)
# Compound Statement Errors
analyzer.statement.compound.missingOpeningBrace=Missing opening brace for compound statement
analyzer.statement.compound.missingOpeningBraceSuggestion=Add an opening brace ''{'' to start the compound statement
analyzer.statement.compound.missingClosingBrace=Missing closing brace for compound statement
analyzer.statement.compound.missingClosingBraceSuggestion=Add a closing brace ''}'' to end the compound statement

# Selection Statement Errors
analyzer.statement.selection.missingIfCondition=Missing condition in if statement
analyzer.statement.selection.missingIfConditionSuggestion=Add a condition expression in parentheses: if (condition)
analyzer.statement.selection.missingSwitchExpression=Missing expression in switch statement
analyzer.statement.selection.missingSwitchExpressionSuggestion=Add an expression in parentheses: switch (expression)
analyzer.statement.selection.missingCaseValue=Missing value in case statement
analyzer.statement.selection.missingCaseValueSuggestion=Add a constant value after ''case'': case value:

# Iteration Statement Errors
analyzer.statement.iteration.missingForCondition=Missing condition in for loop
analyzer.statement.iteration.missingForConditionSuggestion=Add for loop conditions: for (init; condition; increment)
analyzer.statement.iteration.missingWhileCondition=Missing condition in while loop
analyzer.statement.iteration.missingWhileConditionSuggestion=Add a condition expression: while (condition)
analyzer.statement.iteration.missingForeachInitializer=Missing initializer in foreach loop
analyzer.statement.iteration.missingForeachInitializerSuggestion=Add foreach initializer: for (type variable : collection)

# Jump Statement Errors
analyzer.statement.jump.missingSemicolon=Missing semicolon after jump statement
analyzer.statement.jump.missingSemicolonSuggestion=Add a semicolon '';'' after the jump statement

# Expression Statement Errors
analyzer.statement.expression.missingSemicolon=Missing semicolon after expression statement
analyzer.statement.expression.missingSemicolonSuggestion=Add a semicolon '';'' after the expression

# Generic Statement Errors
analyzer.statement.generic=Statement error: {0}
analyzer.statement.genericSuggestion=Check the syntax of the statement

# Expression Error Analyzer Messages (T014)
# Primary Expression Errors
analyzer.expression.primary.missingIdentifier=Missing identifier in primary expression
analyzer.expression.primary.missingIdentifierSuggestion=Add an identifier, literal, or parenthesized expression
analyzer.expression.primary.missingClosingParenthesis=Missing closing parenthesis in primary expression
analyzer.expression.primary.missingClosingParenthesisSuggestion=Add a closing parenthesis '')'' to match the opening parenthesis

# Postfix Expression Errors
analyzer.expression.postfix.missingArrayIndex=Missing array index in postfix expression
analyzer.expression.postfix.missingArrayIndexSuggestion=Add an index expression inside the brackets: [index]
analyzer.expression.postfix.missingFunctionArguments=Missing function arguments in postfix expression
analyzer.expression.postfix.missingFunctionArgumentsSuggestion=Add function arguments inside parentheses: (arg1, arg2, ...)
analyzer.expression.postfix.missingMemberName=Missing member name in postfix expression
analyzer.expression.postfix.missingMemberNameSuggestion=Add a member name after the dot: .memberName

# Assignment Expression Errors
analyzer.expression.assignment.missingRightHandSide=Missing right-hand side in assignment expression
analyzer.expression.assignment.missingRightHandSideSuggestion=Add a value or expression after the assignment operator
analyzer.expression.assignment.invalidLeftHandSide=Invalid left-hand side in assignment expression
analyzer.expression.assignment.invalidLeftHandSideSuggestion=Use a variable, array element, or member access as the left-hand side

# System Variable Access Errors
analyzer.expression.systemVariable.missingName=Missing system variable name after ''@''
analyzer.expression.systemVariable.missingNameSuggestion=Add a system variable name: @variableName or @namespace::variableName

# Signal Reference Errors
analyzer.expression.signal.missingName=Missing signal name after ''$''
analyzer.expression.signal.missingNameSuggestion=Add a signal name: $signalName or $message::signalName

# Delegate Expression Errors
analyzer.expression.delegate.missingBody=Missing body in delegate expression
analyzer.expression.delegate.missingBodySuggestion=Add a compound statement body: delegate() { /* code */ }

# Generic Expression Errors
analyzer.expression.generic=Expression error: {0}
analyzer.expression.genericSuggestion=Check the syntax of the expression

# Structural Error Analyzer Messages (T014A)
# Compilation Unit Errors
analyzer.compilationUnit.missingEOF=Missing end of file marker
analyzer.compilationUnit.missingEOFSuggestion=Ensure the file is properly terminated
analyzer.compilationUnit.invalidStructure=Invalid program structure
analyzer.compilationUnit.invalidStructureSuggestion=Check the order of includes, variables, and handlers sections
analyzer.compilationUnit.missingSectionClosing=Missing closing brace for section
analyzer.compilationUnit.missingSectionClosingSuggestion=Add a closing brace ''}'' to end the section
analyzer.compilationUnit.unexpectedContent=Unexpected content in program structure
analyzer.compilationUnit.unexpectedContentSuggestion=Remove invalid content or place it in the appropriate section
analyzer.compilationUnit.generic=Compilation unit error: {0}
analyzer.compilationUnit.genericSuggestion=Check the overall structure of the CAPL program

# Section Structure Errors - Includes Section
analyzer.sectionStructure.includes.missingOpeningBrace=Missing opening brace for includes section
analyzer.sectionStructure.includes.missingOpeningBraceSuggestion=Add an opening brace ''{'' after ''includes''
analyzer.sectionStructure.includes.missingClosingBrace=Missing closing brace for includes section
analyzer.sectionStructure.includes.missingClosingBraceSuggestion=Add a closing brace ''}'' to end the includes section
analyzer.sectionStructure.includes.invalidDirective=Invalid include directive
analyzer.sectionStructure.includes.invalidDirectiveSuggestion=Use ''#include "filename.cin"'' format for include directives
analyzer.sectionStructure.includes.missingStringLiteral=Missing string literal in include directive
analyzer.sectionStructure.includes.missingStringLiteralSuggestion=Add a quoted filename: #include "filename.cin"

# Section Structure Errors - Variables Section
analyzer.sectionStructure.variables.missingOpeningBrace=Missing opening brace for variables section
analyzer.sectionStructure.variables.missingOpeningBraceSuggestion=Add an opening brace ''{'' after ''variables''
analyzer.sectionStructure.variables.missingClosingBrace=Missing closing brace for variables section
analyzer.sectionStructure.variables.missingClosingBraceSuggestion=Add a closing brace ''}'' to end the variables section
analyzer.sectionStructure.variables.invalidDeclaration=Invalid variable declaration
analyzer.sectionStructure.variables.invalidDeclarationSuggestion=Use proper variable declaration syntax: type variableName;
analyzer.sectionStructure.variables.missingSemicolon=Missing semicolon after variable declaration
analyzer.sectionStructure.variables.missingSemicolonSuggestion=Add a semicolon '';'' after the variable declaration

# Generic Section Structure Errors
analyzer.sectionStructure.generic=Section structure error: {0}
analyzer.sectionStructure.genericSuggestion=Check the syntax of the section structure

# Block Structure Errors
analyzer.blockStructure.missingOpeningBrace=Missing opening brace for block
analyzer.blockStructure.missingOpeningBraceSuggestion=Add an opening brace ''{'' to start the block
analyzer.blockStructure.missingClosingBrace=Missing closing brace for block
analyzer.blockStructure.missingClosingBraceSuggestion=Add a closing brace ''}'' to end the block
analyzer.blockStructure.unmatchedBraces=Unmatched braces in block
analyzer.blockStructure.unmatchedBracesSuggestion=Ensure all opening braces have matching closing braces
analyzer.blockStructure.emptyBlock=Empty block detected
analyzer.blockStructure.emptyBlockSuggestion=Add statements to the block or remove it if not needed
analyzer.blockStructure.invalidContent=Invalid content in block
analyzer.blockStructure.invalidContentSuggestion=Use valid statements or declarations within the block
analyzer.blockStructure.generic=Block structure error: {0}
analyzer.blockStructure.genericSuggestion=Check the syntax of the block structure

# Declaration Error Analyzer Messages (T014B)
# CAPL Variable Declaration Errors
analyzer.caplVarDeclaration.const.missingIdentifier=Missing identifier in const declaration
analyzer.caplVarDeclaration.const.missingIdentifierSuggestion=Add an identifier after ''const'': const identifier = value;
analyzer.caplVarDeclaration.const.missingAssignment=Missing assignment operator in const declaration
analyzer.caplVarDeclaration.const.missingAssignmentSuggestion=Add ''='' after the identifier: const identifier = value;
analyzer.caplVarDeclaration.const.missingValue=Missing value in const declaration
analyzer.caplVarDeclaration.const.missingValueSuggestion=Add a constant value after ''='': const identifier = value;
analyzer.caplVarDeclaration.const.missingSemicolon=Missing semicolon in const declaration
analyzer.caplVarDeclaration.const.missingSemicolonSuggestion=Add a semicolon '';'' at the end of the declaration

# Associative Array Declaration Errors
analyzer.caplVarDeclaration.associativeArray.missingIdentifier=Missing identifier in associative array declaration
analyzer.caplVarDeclaration.associativeArray.missingIdentifierSuggestion=Add an identifier: type identifier[keyType] valueType[];
analyzer.caplVarDeclaration.associativeArray.missingKeyType=Missing key type in associative array declaration
analyzer.caplVarDeclaration.associativeArray.missingKeyTypeSuggestion=Add a key type inside brackets: [keyType]
analyzer.caplVarDeclaration.associativeArray.missingBrackets=Missing brackets in associative array declaration
analyzer.caplVarDeclaration.associativeArray.missingBracketsSuggestion=Add brackets for key and value types: [keyType] valueType[]

# Type Definition Errors
analyzer.caplVarDeclaration.typeDefinition.missingBody=Missing body in type definition
analyzer.caplVarDeclaration.typeDefinition.missingBodySuggestion=Add a body with curly braces: struct/enum name { /* members */ };
analyzer.caplVarDeclaration.typeDefinition.missingSemicolon=Missing semicolon in type definition
analyzer.caplVarDeclaration.typeDefinition.missingSemicolonSuggestion=Add a semicolon '';'' at the end of the type definition

# Regular Declaration Errors
analyzer.caplVarDeclaration.regular.missingTypeSpecifier=Missing type specifier in declaration
analyzer.caplVarDeclaration.regular.missingTypeSpecifierSuggestion=Add a type specifier: int, char, float, message, etc.
analyzer.caplVarDeclaration.regular.missingDeclarator=Missing declarator in declaration
analyzer.caplVarDeclaration.regular.missingDeclaratorSuggestion=Add a variable name after the type specifier

# Generic CAPL Variable Declaration Errors
analyzer.caplVarDeclaration.generic=CAPL variable declaration error: {0}
analyzer.caplVarDeclaration.genericSuggestion=Check the syntax of the variable declaration

# Type Specifier Errors
analyzer.typeSpecifier.builtIn.missingDeclarator=Missing declarator for built-in type
analyzer.typeSpecifier.builtIn.missingDeclaratorSuggestion=Add a variable name after the type
analyzer.typeSpecifier.builtIn.invalidReference=Invalid reference operator for built-in type
analyzer.typeSpecifier.builtIn.invalidReferenceSuggestion=Remove the reference operator or use it in a valid context
analyzer.typeSpecifier.special.missingDeclarator=Missing declarator for {0} type
analyzer.typeSpecifier.special.missingDeclaratorSuggestion=Add a variable name after the {0} type
analyzer.typeSpecifier.config.missingDeclarator=Missing declarator for {0} configuration type
analyzer.typeSpecifier.config.missingDeclaratorSuggestion=Add a variable name after the {0} type
analyzer.typeSpecifier.caplObject.missingParameter=Missing parameter for {0} type
analyzer.typeSpecifier.caplObject.missingParameterSuggestion=Add a parameter for the {0} type (e.g., message ID, signal name)
analyzer.typeSpecifier.caplObject.missingDeclarator=Missing declarator for {0} type
analyzer.typeSpecifier.caplObject.missingDeclaratorSuggestion=Add a variable name after the {0} type
analyzer.typeSpecifier.typedef.missingDeclarator=Missing declarator for typedef name
analyzer.typeSpecifier.typedef.missingDeclaratorSuggestion=Add a variable name after the typedef name
analyzer.typeSpecifier.generic=Type specifier error: {0}
analyzer.typeSpecifier.genericSuggestion=Check the syntax of the type specifier

# Built-in Type Errors
analyzer.builtInType.standardC.missingDeclarator=Missing declarator for {0} type
analyzer.builtInType.standardC.missingDeclaratorSuggestion=Add a variable name after the {0} type
analyzer.builtInType.standardC.invalidCombination=Invalid type combination
analyzer.builtInType.standardC.invalidCombinationSuggestion=Check the type modifiers (signed/unsigned cannot be used with float/double)
analyzer.builtInType.caplIntegral.missingDeclarator=Missing declarator for {0} type
analyzer.builtInType.caplIntegral.missingDeclaratorSuggestion=Add a variable name after the {0} type
analyzer.builtInType.caplIntegral.invalidModifier=Invalid modifier for {0} type
analyzer.builtInType.caplIntegral.invalidModifierSuggestion=Remove signed/unsigned modifiers (not applicable to CAPL integral types)
analyzer.builtInType.composite.missingTypeName=Missing type name for {0}
analyzer.builtInType.composite.missingTypeNameSuggestion=Add a type name after {0}: {0} TypeName
analyzer.builtInType.composite.missingTypeBody=Missing type body for {0}
analyzer.builtInType.composite.missingTypeBodySuggestion=Add a body with curly braces: {0} TypeName { /* members */ }
analyzer.builtInType.composite.missingClosingBrace=Missing closing brace for {0}
analyzer.builtInType.composite.missingClosingBraceSuggestion=Add a closing brace ''}}'' to end the {0} definition
analyzer.builtInType.generic=Built-in type error: {0}
analyzer.builtInType.genericSuggestion=Check the syntax of the built-in type
