# calint Rule Metadata
# This file defines all available linting rules, their categories, descriptions,
# and default severity levels.
#
# Rule ID Format: CALINT-{CATEGORY}-{NNN}
# Categories: SYNTAX, SEMANTIC, STYLE, BEST_PRACTICE, PERFORMANCE
# Severities: ERROR, WARNING, INFO, STYLE

-
  id: "CALINT-SYNTAX-001"
  category: "SYNTAX"
  description: "Detects invalid CAPL object type declarations. Example: `message msg;` should be `message * msg;` or `message 0x100 msg;`"
  defaultSeverity: "ERROR"

-
  id: "CALINT-SYNTAX-002"
  category: "SYNTAX"
  description: "Detects mismatched parentheses, brackets, or braces. Example: `if (a > b { x = 1;`"
  defaultSeverity: "ERROR"

-
  id: "CALINT-STYLE-001"
  category: "STYLE"
  description: "Checks for inconsistent indentation. Recommends using spaces over tabs (configurable)."
  defaultSeverity: "STYLE" # 'STYLE' severity for style-related, non-critical issues

-
  id: "CALINT-STYLE-002"
  category: "STYLE"
  description: "Flags lines exceeding a configurable maximum length."
  defaultSeverity: "STYLE"

-
  id: "CALINT-SEMANTIC-001"
  category: "SEMANTIC"
  description: "Detects use of undeclared variables. Example: `myVar = 10;` where `myVar` was not declared."
  defaultSeverity: "ERROR"

-
  id: "CALINT-SEMANTIC-002"
  category: "SEMANTIC"
  description: "Checks for type mismatches in assignments or operations. Example: `int x = \"text\";`"
  defaultSeverity: "ERROR"

-
  id: "CALINT-BEST_PRACTICE-001"
  category: "BEST_PRACTICE"
  description: "Recommends explicit initialization of variables before use."
  defaultSeverity: "INFO"

-
  id: "CALINT-PERFORMANCE-001"
  category: "PERFORMANCE"
  description: "Identifies potentially inefficient constructs, e.g., loops with invariant calculations inside."
  defaultSeverity: "WARNING"

-
  id: "CALINT-BEST_PRACTICE-002"
  category: "BEST_PRACTICE"
  description: "Flags functions with an excessive number of parameters, which can indicate poor design and reduce readability."
  defaultSeverity: "WARNING"
