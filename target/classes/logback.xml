<configuration>

  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <!-- encoders are assigned the type
         ch.qos.logback.classic.encoder.PatternLayoutEncoder by default -->
    <encoder>
      <pattern>%d{YYYY-MM-dd HH:mm:ss.SSS} [%thread] [%-5level]  %msg%n</pattern>
    </encoder>
  </appender>

  <root level="ERROR">
    <appender-ref ref="STDOUT" />
  </root>

</configuration>
