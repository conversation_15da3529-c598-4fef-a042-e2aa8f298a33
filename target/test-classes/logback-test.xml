<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Test-specific logging configuration -->
    <!-- This file takes precedence over logback.xml during testing -->

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] [%-5level] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Specific logger configurations for test environment -->

    <!-- Suppress CaplWorkspaceService WARN and ERROR logs during testing -->
    <!-- This will suppress "Unknown command" warnings and error stack traces -->
    <logger name="com.polelink.calint.lsp.server.CaplWorkspaceService" level="OFF" additivity="false">
        <appender-ref ref="CONSOLE" />
    </logger>

    <!-- Allow ERROR and WARN logs for CaplAntlrErrorListener to catch real issues -->
    <logger name="com.polelink.calint.parser.CaplAntlrErrorListener" level="WARN" additivity="false">
        <appender-ref ref="CONSOLE" />
    </logger>

    <!-- Allow ERROR logs for transport layer to catch real issues -->
    <logger name="com.polelink.calint.lsp.transport" level="ERROR" additivity="false">
        <appender-ref ref="CONSOLE" />
    </logger>

    <!-- Allow ERROR logs for TransportEventListener to catch real issues -->
    <logger name="TransportEventListener" level="ERROR" additivity="false">
        <appender-ref ref="CONSOLE" />
    </logger>

    <!-- Allow WARN and ERROR logs for parser to catch real issues -->
    <logger name="com.polelink.calint.parser" level="WARN" additivity="false">
        <appender-ref ref="CONSOLE" />
    </logger>

    <!-- Keep CLI and other components at INFO level for useful test output -->
    <logger name="com.polelink.calint.cli" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE" />
    </logger>

    <!-- Keep text document service at INFO level -->
    <logger name="com.polelink.calint.lsp.server.CaplTextDocumentService" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE" />
    </logger>

    <!-- Keep language server at INFO level -->
    <logger name="com.polelink.calint.lsp.server.CaplLanguageServer" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE" />
    </logger>

    <!-- Suppress regression test output -->
    <logger name="com.polelink.calint.regression" level="OFF" additivity="false">
        <appender-ref ref="CONSOLE" />
    </logger>

    <!-- Root logger configuration - allow WARN and ERROR to catch real issues -->
    <root level="WARN">
        <appender-ref ref="CONSOLE" />
    </root>
</configuration>
